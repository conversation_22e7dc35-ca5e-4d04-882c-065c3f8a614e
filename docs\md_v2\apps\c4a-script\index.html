<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C4A-Script Interactive Tutorial | Crawl4AI</title>
    <link rel="stylesheet" href="assets/app.css">
    <link rel="stylesheet" href="assets/blockly-theme.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/theme/material-darker.min.css">
</head>
<body>
    <!-- Tutorial Intro Modal -->
    <div id="tutorial-intro" class="tutorial-intro-modal">
        <div class="intro-content">
            <h2>Welcome to C4A-Script Tutorial!</h2>
            <p>C4A-Script is a simple language for web automation. This interactive tutorial will teach you:</p>
            <ul>
                <li>How to handle popups and banners</li>
                <li>Form filling and navigation</li>
                <li>Advanced automation techniques</li>
            </ul>
            <div class="intro-actions">
                <button id="start-tutorial-btn" class="intro-btn primary">Start Tutorial</button>
                <button id="skip-tutorial-btn" class="intro-btn">Skip</button>
            </div>
        </div>
    </div>
    
    <!-- Event Editor Modal -->
    <div id="event-editor-overlay" class="modal-overlay hidden"></div>
    <div id="event-editor-modal" class="event-editor-modal hidden">
        <h4>Edit Event</h4>
        <div class="editor-field">
            <label>Command Type</label>
            <select id="edit-command-type" disabled>
                <option value="CLICK">CLICK</option>
                <option value="DOUBLE_CLICK">DOUBLE_CLICK</option>
                <option value="RIGHT_CLICK">RIGHT_CLICK</option>
                <option value="TYPE">TYPE</option>
                <option value="SET">SET</option>
                <option value="SCROLL">SCROLL</option>
                <option value="WAIT">WAIT</option>
            </select>
        </div>
        <div id="edit-selector-field" class="editor-field">
            <label>Selector</label>
            <input type="text" id="edit-selector" placeholder=".class or #id">
        </div>
        <div id="edit-value-field" class="editor-field">
            <label>Value</label>
            <input type="text" id="edit-value" placeholder="Text or number">
        </div>
        <div id="edit-direction-field" class="editor-field hidden">
            <label>Direction</label>
            <select id="edit-direction">
                <option value="UP">UP</option>
                <option value="DOWN">DOWN</option>
                <option value="LEFT">LEFT</option>
                <option value="RIGHT">RIGHT</option>
            </select>
        </div>
        <div class="editor-actions">
            <button id="edit-cancel" class="mini-btn">Cancel</button>
            <button id="edit-save" class="mini-btn primary">Save</button>
        </div>
    </div>

    <!-- Main App Layout -->
    <div class="app-container">
        <!-- Left Panel: Editor -->
        <div class="editor-panel">
            <div class="panel-header">
                <h2>C4A-Script Editor</h2>
                <div class="header-actions">
                    <button id="tutorial-btn" class="action-btn" title="Tutorial">
                        <span class="icon">📚</span>
                    </button>
                    <button id="examples-btn" class="action-btn" title="Examples">
                        <span class="icon">📋</span>
                    </button>
                    <button id="clear-btn" class="action-btn" title="Clear">
                        <span class="icon">🗑</span>
                    </button>
                    <button id="run-btn" class="action-btn primary">
                        <span class="icon">▶</span>Run
                    </button>
                    <button id="record-btn" class="action-btn record">
                        <span class="icon">⏺</span>Record
                    </button>
                    <button id="timeline-btn" class="action-btn timeline hidden" title="View Timeline">
                        <span class="icon">📊</span>
                    </button>
                </div>
            </div>
            
            <div class="editor-container">
                <div id="editor-view" class="editor-wrapper">
                    <textarea id="c4a-editor" placeholder="# Write your C4A script here..."></textarea>
                </div>
                
                <!-- Recording Timeline -->
                <div id="timeline-view" class="recording-timeline hidden">
                    <div class="timeline-header">
                        <h3>Recording Timeline</h3>
                        <div class="timeline-actions">
                            <button id="back-to-editor" class="mini-btn">← Back</button>
                            <button id="select-all-events" class="mini-btn">Select All</button>
                            <button id="clear-events" class="mini-btn">Clear</button>
                            <button id="generate-script" class="mini-btn primary">Generate Script</button>
                        </div>
                    </div>
                    <div id="timeline-events" class="timeline-events">
                        <!-- Events will be added here dynamically -->
                    </div>
                </div>
            </div>
            
            <!-- Bottom: Output Tabs -->
            <div class="output-section">
                <div class="tabs">
                    <button class="tab active" data-tab="console">Console</button>
                    <button class="tab" data-tab="javascript">Generated JS</button>
                </div>
                <div class="tab-content">
                    <div id="console-tab" class="tab-pane active">
                        <div id="console-output" class="console">
                            <div class="console-line">
                                <span class="console-prompt">$</span>
                                <span class="console-text">Ready to run C4A scripts...</span>
                            </div>
                        </div>
                    </div>
                    <div id="javascript-tab" class="tab-pane">
                        <div class="js-output-header">
                            <div class="js-actions">
                                <button id="copy-js-btn" class="mini-btn" title="Copy">
                                    <span>📋</span>
                                </button>
                                <button id="edit-js-btn" class="mini-btn" title="Edit">
                                    <span>✏️</span>
                                </button>
                            </div>
                        </div>
                        <pre id="js-output" class="js-output">// JavaScript will appear here...</pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel: Playground -->
        <div class="playground-panel">
            <div class="panel-header">
                <h2>Playground</h2>
                <div class="header-actions">
                    <button id="reset-playground" class="action-btn" title="Reset">
                        <span class="icon">🔄</span>
                    </button>
                    <button id="fullscreen-btn" class="action-btn" title="Fullscreen">
                        <span class="icon">⛶</span>
                    </button>
                </div>
            </div>
            <div class="playground-wrapper">
                <iframe id="playground-frame" src="playground/" title="Playground"></iframe>
            </div>
        </div>
    </div>

    <!-- Tutorial Navigation Bar -->
    <div id="tutorial-nav" class="tutorial-nav hidden">
        <div class="tutorial-nav-content">
            <div class="tutorial-left">
                <div class="tutorial-step-title">
                    <span id="tutorial-step-info">Step 1 of 9</span>
                    <span id="tutorial-title">Welcome</span>
                </div>
                <p id="tutorial-description" class="tutorial-description">Let's start by waiting for the page to load.</p>
            </div>
            <div class="tutorial-right">
                <div class="tutorial-controls">
                    <button id="tutorial-prev" class="nav-btn" disabled>← Previous</button>
                    <button id="tutorial-next" class="nav-btn primary">Next →</button>
                </div>
                <button id="tutorial-exit" class="exit-btn" title="Exit Tutorial">×</button>
            </div>
        </div>
        <div class="tutorial-progress-bar">
            <div id="tutorial-progress-fill" class="progress-fill"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/6.65.7/mode/javascript/javascript.min.js"></script>
    
    <!-- Blockly -->
    <script src="https://unpkg.com/blockly/blockly.min.js"></script>
    <script src="assets/c4a-blocks.js"></script>
    <script src="assets/c4a-generator.js"></script>
    <script src="assets/blockly-manager.js"></script>
    
    <script src="assets/app.js"></script>
</body>
</html>