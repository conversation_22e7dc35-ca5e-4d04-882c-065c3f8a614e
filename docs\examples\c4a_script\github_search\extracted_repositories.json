[{"repository_name": "unclecode/crawl4ai", "repository_owner": "unclecode/crawl4ai", "repository_url": "/unclecode/crawl4ai", "description": "🚀🤖Crawl4AI: Open-source LLM Friendly Web Crawler & Scraper. Don't be shy, join here:https://discord.gg/jP8KfhDhyN", "primary_language": "Python", "star_count": "45.1k", "topics": [], "last_updated": "23 hours ago"}, {"repository_name": "coleam00/mcp-crawl4ai-rag", "repository_owner": "coleam00/mcp-crawl4ai-rag", "repository_url": "/coleam00/mcp-crawl4ai-rag", "description": "Web Crawling and RAG Capabilities for AI Agents and AI Coding Assistants", "primary_language": "Python", "star_count": "748", "topics": [], "last_updated": "yesterday"}, {"repository_name": "pdichone/crawl4ai-rag-system", "repository_owner": "pdichone/crawl4ai-rag-system", "repository_url": "/pdichone/crawl4ai-rag-system", "primary_language": "Python", "star_count": "44", "topics": [], "last_updated": "on 21 Jan"}, {"repository_name": "weidwonder/crawl4ai-mcp-server", "repository_owner": "weidwonder/crawl4ai-mcp-server", "repository_url": "/weidwonder/crawl4ai-mcp-server", "description": "用于提供给本地开发者的 LLM的高效互联网搜索&内容获取的MCP Server， 节省你的token", "primary_language": "Python", "star_count": "87", "topics": [], "last_updated": "24 days ago"}, {"repository_name": "leonardogrig/crawl4ai-deepseek-example", "repository_owner": "leonardogrig/crawl4ai-deepseek-example", "repository_url": "/leonardogrig/crawl4ai-deepseek-example", "primary_language": "Python", "star_count": "29", "topics": [], "last_updated": "on 18 Jan"}, {"repository_name": "laurentvv/crawl4ai-mcp", "repository_owner": "laurentvv/crawl4ai-mcp", "repository_url": "/laurentvv/crawl4ai-mcp", "description": "Web crawling tool that integrates with AI assistants via the MCP", "primary_language": "Python", "star_count": "10", "topics": [{}, {}, {}, {}, {}], "last_updated": "on 16 Mar"}, {"repository_name": "kaymen99/ai-web-scraper", "repository_owner": "kaymen99/ai-web-scraper", "repository_url": "/kaymen99/ai-web-scraper", "description": "AI web scraper built withCrawl4AIfor extracting structured leads data from websites.", "primary_language": "Python", "star_count": "30", "topics": [{}, {}, {}, {}, {}], "last_updated": "on 13 Feb"}, {"repository_name": "atakkant/ai_web_crawler", "repository_owner": "atakkant/ai_web_crawler", "repository_url": "/atakkant/ai_web_crawler", "description": "crawl4ai, DeepSeek, Groq", "primary_language": "Python", "star_count": "9", "topics": [], "last_updated": "on 19 Feb"}, {"repository_name": "Croups/auto-scraper-with-llms", "repository_owner": "Croups/auto-scraper-with-llms", "repository_url": "/Croups/auto-scraper-with-llms", "description": "Web scraping AI that leverages thecrawl4ailibrary to extract structured data from web pages using various large language models (LLMs).", "primary_language": "Python", "star_count": "49", "topics": [], "last_updated": "on 8 Apr"}, {"repository_name": "leonardogrig/crawl4ai_llm_examples", "repository_owner": "leonardogrig/crawl4ai_llm_examples", "repository_url": "/leonardogrig/crawl4ai_llm_examples", "primary_language": "Python", "star_count": "8", "topics": [], "last_updated": "on 29 Jan"}]