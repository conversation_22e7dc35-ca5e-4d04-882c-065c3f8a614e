<div id="nav-belt" style="width: 100%;">
    <div class="nav-left">
        <script type="text/javascript">window.navmet.tmp = +new Date();</script>
        <div id="nav-logo">
            <a href="/ref=nav_logo" id="nav-logo-sprites" class="nav-logo-link nav-progressive-attribute"
                aria-label="Amazon" lang="en">
                <span class="nav-sprite nav-logo-base"></span>
                <span id="logo-ext" class="nav-sprite nav-logo-ext nav-progressive-content"></span>
                <span class="nav-logo-locale">.us</span>
            </a>
        </div>
        <script
            type="text/javascript">window.navmet.push({ key: 'Logo', end: +new Date(), begin: window.navmet.tmp });</script>

        <div id="nav-global-location-slot">
            <span id="nav-global-location-data-modal-action" class="a-declarative nav-progressive-attribute"
                data-a-modal="{&quot;width&quot;:375, &quot;closeButton&quot;:&quot;true&quot;,&quot;popoverLabel&quot;:&quot;Choose your location&quot;, &quot;ajaxHeaders&quot;:{&quot;anti-csrftoken-a2z&quot;:&quot;hHBwllskaYQrylaW9ifYQIdmqBZOtGdKro0TWb5kDoPKAAAAAGhEMhsAAAAB&quot;}, &quot;name&quot;:&quot;glow-modal&quot;, &quot;url&quot;:&quot;/portal-migration/hz/glow/get-rendered-address-selections?deviceType=desktop&amp;pageType=Gateway&amp;storeContext=NoStoreName&amp;actionSource=desktop-modal&quot;, &quot;footer&quot;:&quot;<span class=\&quot;a-declarative\&quot; data-action=\&quot;a-popover-close\&quot; data-a-popover-close=\&quot;{}\&quot;><span class=\&quot;a-button a-button-primary\&quot;><span class=\&quot;a-button-inner\&quot;><button name=\&quot;glowDoneButton\&quot; class=\&quot;a-button-text\&quot; type=\&quot;button\&quot;>Done</button></span></span></span>&quot;,&quot;header&quot;:&quot;Choose your location&quot;}"
                data-action="a-modal">
                <a id="nav-global-location-popover-link" role="button" tabindex="0"
                    class="nav-a nav-a-2 a-popover-trigger a-declarative nav-progressive-attribute" href="">
                    <div class="nav-sprite nav-progressive-attribute" id="nav-packard-glow-loc-icon"></div>
                    <div id="glow-ingress-block">
                        <span class="nav-line-1 nav-progressive-content" id="glow-ingress-line1">
                            Deliver to
                        </span>
                        <span class="nav-line-2 nav-progressive-content" id="glow-ingress-line2">
                            Malaysia
                        </span>
                    </div>
                </a>
            </span>
            <input data-addnewaddress="add-new" id="unifiedLocation1ClickAddress" name="dropdown-selection"
                type="hidden" value="add-new" class="nav-progressive-attribute">
            <input data-addnewaddress="add-new" id="ubbShipTo" name="dropdown-selection-ubb" type="hidden"
                value="add-new" class="nav-progressive-attribute">
            <input id="glowValidationToken" name="glow-validation-token" type="hidden"
                value="hHBwllskaYQrylaW9ifYQIdmqBZOtGdKro0TWb5kDoPKAAAAAGhEMhsAAAAB" class="nav-progressive-attribute">
            <input id="glowDestinationType" name="glow-destination-type" type="hidden" value="COUNTRY"
                class="nav-progressive-attribute">
        </div>

        <div id="nav-global-location-toaster-script-container" class="nav-progressive-content">
            <!-- NAVYAAN-GLOW-NAV-TOASTER -->
            <script>
                P.when('glow-toaster-strings').execute(function (S) {
                    S.load({ "glow-toaster-address-change-error": "An error has occurred and the address has not been updated. Please try again.", "glow-toaster-unknown-error": "An error has occurred. Please try again." });
                });
            </script>
            <script>
                P.when('glow-toaster-manager').execute(function (M) {
                    M.create({ "pageType": "Gateway", "aisTransitionState": null, "rancorLocationSource": "REALM_DEFAULT" })
                });
            </script>
        </div>

    </div>
    <div class="nav-fill" id="nav-fill-search">
        <script type="text/javascript">window.navmet.tmp = +new Date();</script>
        <div id="nav-search">
            <div id="nav-bar-left"></div>
            <form id="nav-search-bar-form" accept-charset="utf-8" action="/s/ref=nb_sb_noss_1"
                class="nav-searchbar nav-progressive-attribute" method="GET" name="site-search" role="search">

                <div class="nav-left">
                    <div id="nav-search-dropdown-card">

                        <div class="nav-search-scope nav-sprite">
                            <div class="nav-search-facade" data-value="search-alias=aps">
                                <span id="nav-search-label-id" class="nav-search-label nav-progressive-content"
                                    style="width: auto;">All</span>
                                <i class="nav-icon"></i>
                            </div>
                            <label id="searchDropdownDescription" for="searchDropdownBox"
                                class="nav-progressive-attribute" style="display:none">Select the department you want to
                                search in</label>
                            <select aria-describedby="searchDropdownDescription"
                                class="nav-search-dropdown searchSelect nav-progressive-attrubute nav-progressive-search-dropdown"
                                data-nav-digest="k+fyIAyB82R9jVEmroQ0OWwSW3A=" data-nav-selected="0"
                                id="searchDropdownBox" name="url" style="display: block; top: 2.5px;" tabindex="0"
                                title="Search in">
                                <option selected="selected" value="search-alias=aps">All Departments</option>
                                <option value="search-alias=arts-crafts-intl-ship">Arts &amp; Crafts</option>
                                <option value="search-alias=automotive-intl-ship">Automotive</option>
                                <option value="search-alias=baby-products-intl-ship">Baby</option>
                                <option value="search-alias=beauty-intl-ship">Beauty &amp; Personal Care</option>
                                <option value="search-alias=stripbooks-intl-ship">Books</option>
                                <option value="search-alias=fashion-boys-intl-ship">Boys' Fashion</option>
                                <option value="search-alias=computers-intl-ship">Computers</option>
                                <option value="search-alias=deals-intl-ship">Deals</option>
                                <option value="search-alias=digital-music">Digital Music</option>
                                <option value="search-alias=electronics-intl-ship">Electronics</option>
                                <option value="search-alias=fashion-girls-intl-ship">Girls' Fashion</option>
                                <option value="search-alias=hpc-intl-ship">Health &amp; Household</option>
                                <option value="search-alias=kitchen-intl-ship">Home &amp; Kitchen</option>
                                <option value="search-alias=industrial-intl-ship">Industrial &amp; Scientific</option>
                                <option value="search-alias=digital-text">Kindle Store</option>
                                <option value="search-alias=luggage-intl-ship">Luggage</option>
                                <option value="search-alias=fashion-mens-intl-ship">Men's Fashion</option>
                                <option value="search-alias=movies-tv-intl-ship">Movies &amp; TV</option>
                                <option value="search-alias=music-intl-ship">Music, CDs &amp; Vinyl</option>
                                <option value="search-alias=pets-intl-ship">Pet Supplies</option>
                                <option value="search-alias=instant-video">Prime Video</option>
                                <option value="search-alias=software-intl-ship">Software</option>
                                <option value="search-alias=sporting-intl-ship">Sports &amp; Outdoors</option>
                                <option value="search-alias=tools-intl-ship">Tools &amp; Home Improvement</option>
                                <option value="search-alias=toys-and-games-intl-ship">Toys &amp; Games</option>
                                <option value="search-alias=videogames-intl-ship">Video Games</option>
                                <option value="search-alias=fashion-womens-intl-ship">Women's Fashion</option>
                            </select>
                        </div>

                    </div>
                </div>
                <div class="nav-fill">
                    <div class="nav-search-field ">
                        <label for="twotabsearchtextbox" style="display: none;">Search Amazon</label>
                        <input type="text" id="twotabsearchtextbox" value="" name="field-keywords" autocomplete="off"
                            placeholder="Search Amazon" class="nav-input nav-progressive-attribute" dir="auto"
                            tabindex="0" aria-label="Search Amazon" role="searchbox" aria-autocomplete="list"
                            aria-controls="sac-autocomplete-results-container" aria-expanded="false"
                            aria-haspopup="grid" spellcheck="false">
                    </div>
                    <div id="nav-iss-attach"></div>
                </div>
                <div class="nav-right">
                    <div class="nav-search-submit nav-sprite">
                        <span id="nav-search-submit-text"
                            class="nav-search-submit-text nav-sprite nav-progressive-attribute" aria-label="Go">
                            <input id="nav-search-submit-button" type="submit"
                                class="nav-input nav-progressive-attribute" value="Go" tabindex="0">
                        </span>
                    </div>
                </div>
                <input type="hidden" id="isscrid" name="crid" value="15O5T5OCG5OZE"><input type="hidden" id="issprefix"
                    name="sprefix" value="r2d2,aps,588">
            </form>
        </div>
        <script
            type="text/javascript">window.navmet.push({ key: 'Search', end: +new Date(), begin: window.navmet.tmp });</script>
    </div>
    <div class="nav-right">
        <script type="text/javascript">window.navmet.tmp = +new Date();</script>
        <div id="nav-tools" class="layoutToolbarPadding">




            <div class="nav-div" id="icp-nav-flyout">
                <a href="/customer-preferences/edit?ie=UTF8&amp;preferencesReturnUrl=%2F&amp;ref_=topnav_lang_ais"
                    class="nav-a nav-a-2 icp-link-style-2" aria-label="Choose a language for shopping in Amazon United States. The current selection is English (EN).
">
                    <span class="icp-nav-link-inner">
                        <span class="nav-line-1">
                        </span>
                        <span class="nav-line-2">
                            <span class="icp-nav-flag icp-nav-flag-us icp-nav-flag-lop" role="img"
                                aria-label="United States"></span>
                            <div>EN</div>
                        </span>
                    </span>
                </a>
                <button class="nav-flyout-button nav-icon nav-arrow" aria-label="Expand to Change Language or Country"
                    tabindex="0" style="visibility: visible;"></button>
            </div>


            <div class="nav-div" id="nav-link-accountList">
                <a href="https://www.amazon.com/ap/signin?openid.pape.max_auth_age=0&amp;openid.return_to=https%3A%2F%2Fwww.amazon.com%2F%3Fref_%3Dnav_ya_signin&amp;openid.identity=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&amp;openid.assoc_handle=usflex&amp;openid.mode=checkid_setup&amp;openid.claimed_id=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0%2Fidentifier_select&amp;openid.ns=http%3A%2F%2Fspecs.openid.net%2Fauth%2F2.0"
                    class="nav-a nav-a-2   nav-progressive-attribute" data-nav-ref="nav_ya_signin"
                    data-nav-role="signin" data-ux-jq-mouseenter="true" tabindex="0" data-csa-c-type="link"
                    data-csa-c-slot-id="nav-link-accountList" data-csa-c-content-id="nav_ya_signin"
                    aria-controls="nav-flyout-accountList" data-csa-c-id="37vs0l-z575id-52hnw3-x34ncp">
                    <div class="nav-line-1-container"><span id="nav-link-accountList-nav-line-1"
                            class="nav-line-1 nav-progressive-content">Hello, sign in</span></div>
                    <span class="nav-line-2 ">Account &amp; Lists
                    </span>
                </a>
                <button class="nav-flyout-button nav-icon nav-arrow" aria-label="Expand Account and Lists" tabindex="0"
                    style="visibility: visible;"></button>
            </div>


            <a href="/gp/css/order-history?ref_=nav_orders_first" class="nav-a nav-a-2   nav-progressive-attribute"
                id="nav-orders" tabindex="0">
                <span class="nav-line-1">Returns</span>
                <span class="nav-line-2">&amp; Orders<span class="nav-icon nav-arrow"></span></span>
            </a>



            <a href="/gp/cart/view.html?ref_=nav_cart" aria-label="0 items in cart"
                class="nav-a nav-a-2 nav-progressive-attribute" id="nav-cart">
                <div id="nav-cart-count-container">
                    <span id="nav-cart-count" aria-hidden="true"
                        class="nav-cart-count nav-cart-0 nav-progressive-attribute nav-progressive-content">0</span>
                    <span class="nav-cart-icon nav-sprite"></span>
                </div>
                <div id="nav-cart-text-container" class=" nav-progressive-attribute">
                    <span aria-hidden="true" class="nav-line-1">

                    </span>
                    <span aria-hidden="true" class="nav-line-2">
                        Cart
                        <span class="nav-icon nav-arrow"></span>
                    </span>
                </div>
            </a>

        </div>
        <script
            type="text/javascript">window.navmet.push({ key: 'Tools', end: +new Date(), begin: window.navmet.tmp });</script>

    </div>
</div>