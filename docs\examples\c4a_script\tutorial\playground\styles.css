/* Playground Styles - Modern Web App Theme */
:root {
    --primary-color: #0fbbaa;
    --secondary-color: #3f3f44;
    --background-color: #ffffff;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --error-color: #ff3c74;
    --success-color: #0fbbaa;
    --warning-color: #ffa500;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* <PERSON><PERSON> Banner */
.cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #2c3e50;
    color: white;
    padding: 1rem;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.cookie-actions {
    display: flex;
    gap: 0.5rem;
}

/* Header */
.site-header {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.2s;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    z-index: 1;
    border-radius: 4px;
    top: 100%;
    margin-top: 0.5rem;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    color: var(--text-color);
    padding: 0.75rem 1rem;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    background-color: #f5f5f5;
}

/* Auth Section */
.auth-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-avatar {
    font-size: 1.5rem;
}

/* Main Content */
.main-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* Buttons */
.btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s;
}

.btn:hover {
    background-color: #0aa599;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #333;
}

.btn-primary {
    background-color: var(--primary-color);
}

/* Feature Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature-card {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.2s;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.feature-card h3 {
    margin-top: 0;
}

/* Modal */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    position: relative;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-message {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: 4px;
    display: none;
}

.form-message.error {
    background-color: #ffe6e6;
    color: var(--error-color);
    display: block;
}

.form-message.success {
    background-color: #e6fff6;
    color: var(--success-color);
    display: block;
}

/* Product Catalog */
.view-toggle {
    margin-bottom: 1rem;
}

.catalog-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
}

.filters-sidebar {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.filter-group {
    margin-bottom: 1.5rem;
}

.collapsible {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-content {
    margin-top: 0.5rem;
}

.filter-content label {
    display: block;
    margin-bottom: 0.5rem;
}

/* Product Grid */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.product-card {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.product-image {
    width: 100%;
    height: 150px;
    background-color: #f0f0f0;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
}

.product-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.product-price {
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 700;
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: 2rem;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 2rem;
}

.page-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background-color: white;
    cursor: pointer;
    border-radius: 4px;
}

.page-btn:hover,
.page-btn.active {
    background-color: var(--primary-color);
    color: white;
}

/* Multi-step Form */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    margin-bottom: 2rem;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 4px;
    transition: width 0.3s;
}

.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

/* Tabs */
.tabs-container {
    margin-top: 2rem;
}

.tabs-header {
    display: flex;
    border-bottom: 2px solid var(--border-color);
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    position: relative;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

.tabs-content {
    padding: 2rem 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Expandable Text */
.expandable-text {
    margin-top: 1rem;
}

.text-preview {
    margin-bottom: 0.5rem;
}

.show-more {
    margin-top: 0.5rem;
}

/* Comments Section */
.comments-section {
    margin-top: 1rem;
}

.comment {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.comment-author {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Data Table */
.table-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-input {
    flex: 1;
    max-width: 300px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.sortable {
    cursor: pointer;
}

.sortable:hover {
    color: var(--primary-color);
}

/* Form Cards */
.form-card {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-card h2 {
    margin-top: 0;
}

/* Success Message */
.success-message {
    background-color: #e6fff6;
    color: var(--success-color);
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
    font-weight: 500;
}

/* Load More Button */
.load-more,
.load-more-rows {
    display: block;
    margin: 2rem auto;
}

/* Responsive */
@media (max-width: 768px) {
    .catalog-layout {
        grid-template-columns: 1fr;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .cookie-content {
        flex-direction: column;
        text-align: center;
    }
}

/* Inspector Mode */
#inspector-btn.active {
    background: var(--primary-color) !important;
    color: var(--bg-primary) !important;
}

.inspector-tooltip {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
