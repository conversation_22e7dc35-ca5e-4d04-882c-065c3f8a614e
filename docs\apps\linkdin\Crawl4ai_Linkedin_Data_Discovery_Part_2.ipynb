{"cells": [{"cell_type": "markdown", "metadata": {"id": "nhF2o_bUd2XI"}, "source": ["# 🧠 Advanced LinkedIn Insights with Crawl4AI: Knowledge Graph & AI Analysis\n", "\n", "## Welcome to Part 2!\n", "\n", "In **Part 1**, we extracted LinkedIn company and people data. Now, we'll transform that raw data into actionable insights:\n", "\n", "- 🕸️ **Build a company similarity graph** using semantic embeddings\n", "- 🏢 **Infer organizational structures** with LLM analysis\n", "- 🎯 **Identify key decision makers** through influence scoring\n", "- 🌐 **Create an interactive knowledge graph** visualization\n", "- 💬 **Enable AI-powered queries** on your data\n", "\n", "If you haven't completed Part 1, please start there to generate the required data files, or use our sample data to follow along.\n", "- **Part 1**: [Data\n", "  Extraction](https://colab.research.google.com/drive/10nRCwmfxPjVrRUHyJsYlX7BH5bvPoGpx)\n", "   - Extract LinkedIn company and people data\n", "   \n", "## What You'll Build\n", "\n", "An **interactive B2B intelligence dashboard** that:\n", "1. Shows companies as nodes in a network graph\n", "2. Visualizes organizational hierarchies\n", "3. Highlights decision makers with influence scores\n", "4. Enables chat-based exploration of the data\n", "\n", "## Prerequisites\n", "\n", "- Google Colab (free tier sufficient)\n", "- **Complete Part 1 first**: [📊 LinkedIn Data Extraction\n", "  Workshop](https://colab.research.google.com/drive/10nRCwmfxPjVrRUHyJsYlX7BH5bvPoGpx)\n", "    - Part 1 generates the `companies.jsonl` and `people.jsonl` files needed here\n", "    - If you haven't completed Part 1, you can still follow along using sample data\n", "- LLM API key for org chart inference (Gemini recommended)\n", "- OpenAI API key for chat functionality (optional)\n", "\n", "## Pipeline Overview\n", "\n", "```\n", "Raw Data → Embeddings → Similarity Graph → Org Charts → Decision Makers → Visualization\n", "```\n", "\n", "  ## Pipeline Overview\n", "\n", "  This notebook implements an 8-step pipeline to transform raw LinkedIn data into\n", "  actionable B2B intelligence:\n", "\n", "  1. **Setup & Dependencies** → Install required libraries and prepare environment\n", "  2. **Data Loading** → Import companies and people data from Part 1\n", "  3. **Semantic Embeddings** → Convert company descriptions into mathematical\n", "  representations\n", "  4. **Similarity Graph** → Build a network showing company relationships\n", "  5. **Organizational Inference** → Use AI to understand company hierarchies\n", "  6. **Decision Maker Identification** → Score and extract key contacts\n", "  7. **Visualization Generation** → Create interactive dashboard files\n", "  8. **Interactive Display** → Launch and view the knowledge graph\n", "\n", "  Each step builds upon the previous, creating a complete intelligence system.\n", "\n", "Let's begin! 🚀"]}, {"cell_type": "markdown", "source": [], "metadata": {"id": "hGkQINWmrNuA"}}, {"cell_type": "markdown", "metadata": {"id": "mhJgiWYZd2XJ"}, "source": ["## Step 0: Setup and Dependencies\n", "\n", "  In this step, we prepare the Colab environment for our insights pipeline. We clone the\n", "   Crawl4AI repository to access template files and install essential libraries:\n", "  sentence-transformers for creating semantic embeddings, litellm for LLM integration,\n", "  and data processing tools. This foundation ensures all subsequent steps have the\n", "  necessary resources and dependencies to execute smoothly."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "717tTpmad2XK"}, "outputs": [], "source": ["%%capture\n", "# Clone the repository and copy necessary files\n", "!git clone -b next https://github.com/unclecode/crawl4ai.git\n", "!cp -r /content/crawl4ai/docs/apps/linkdin/{templates,samples} /content/\n", "!mkdir -p /content/output"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "c1Zs5nYId2XK", "outputId": "effbad97-bf14-444e-8332-679d14394e00"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.0/8.0 MB\u001b[0m \u001b[31m76.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m38.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m34.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m24.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m9.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m5.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m32.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["# Install required packages\n", "!pip install -q sentence-transformers litellm pandas numpy scikit-learn"]}, {"cell_type": "markdown", "metadata": {"id": "3RGkhCXsd2XK"}, "source": ["## Step 1: Import Libraries and Configuration\n", "\n", "  Here we import the LinkedIn data extracted in Part 1 of the workshop. The pipeline\n", "  accepts two JSONL files: companies.jsonl (containing company profiles, descriptions,\n", "  and metadata) and people.jsonl (containing employee information linked to companies).\n", "  Users can either upload their own data or use provided samples. This data serves as\n", "  the raw material for building our knowledge graph and organizational insights."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XPz492Gid2XK", "outputId": "e84f6a1a-7c08-43f3-8b76-8a3d32c7008f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Libraries imported successfully!\n"]}], "source": ["import json\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "from datetime import datetime\n", "from collections import defaultdict\n", "from typing import List, Dict, Any, Optional, Tuple\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# For embeddings and similarity\n", "from sentence_transformers import SentenceTransformer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "# For LLM inference\n", "import litellm\n", "from google.colab import userdata\n", "\n", "# Configuration\n", "SIMILARITY_THRESHOLD = 0.3\n", "INDUSTRY_WEIGHT_BONUS = 0.10\n", "GEO_WEIGHT_BONUS = 0.05\n", "DECISION_THRESHOLD = 0.5\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "wFAVEUEnd2XK"}, "source": ["## Step 2: Load Data\n", "\n", "  Here we import the LinkedIn data extracted in Part 1 of the workshop. The pipeline\n", "  accepts two JSONL files: companies.jsonl (containing company profiles, descriptions,\n", "  and metadata) and people.jsonl (containing employee information linked to companies).\n", "  Users can either upload their own data or use provided samples. This data serves as\n", "  the raw material for building our knowledge graph and organizational insights.\n", "\n", "Upload your `companies.jsonl` and `people.jsonl` files from Part 1, or use the sample data."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 229}, "id": "4uiMHAjgd2XK", "outputId": "f1069602-c6fc-49d3-dc9f-6572fb56d7f9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📤 Please upload your data files:\n", "1. companies.jsonl\n", "2. people.jsonl\n", "\n", "Or press Cancel to use sample data...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-b53657a7-fc08-4fe7-b470-ee564bfe03aa\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-b53657a7-fc08-4fe7-b470-ee564bfe03aa\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving companies.jsonl to companies.jsonl\n", "Saving people.jsonl to people.jsonl\n", "\n", "✅ Files uploaded successfully!\n"]}], "source": ["# Option 1: Upload your own files\n", "from google.colab import files\n", "import shutil\n", "\n", "print(\"📤 Please upload your data files:\")\n", "print(\"1. companies.jsonl\")\n", "print(\"2. people.jsonl\")\n", "print(\"\\nOr press Cancel to use sample data...\")\n", "\n", "try:\n", "    uploaded = files.upload()\n", "\n", "    # Move uploaded files to output directory\n", "    for filename in uploaded.keys():\n", "        shutil.move(filename, f'/content/output/{filename}')\n", "    print(\"\\n✅ Files uploaded successfully!\")\n", "except:\n", "    # Option 2: Use sample data\n", "    print(\"\\n📁 Using sample data...\")\n", "    !cp /content/samples/*.jsonl /content/output/\n", "    print(\"✅ Sample data loaded!\")\n", "\n", "# Check files are created, if not simply copy from samples to output\n", "if not Path('/content/output/companies.jsonl').exists():\n", "    !cp /content/samples/companies.jsonl /content/output/\n", "\n", "if not Path('/content/output/people.jsonl').exists():\n", "    !cp /content/samples/people.jsonl /content/output/"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "w8DGmbsLd2XK", "outputId": "2f254d14-9a5a-4bce-f759-0970b5be9c30"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📊 Loaded 5 companies and 24 people\n", "\n", "🏢 Sample company: Posify\n", "👤 Sample person: <PERSON><PERSON>\n"]}], "source": ["# Load the data\n", "def load_jsonl(path: str) -> List[Dict]:\n", "    \"\"\"Load JSONL file into list of dictionaries\"\"\"\n", "    data = []\n", "    with open(path, 'r') as f:\n", "        for line in f:\n", "            data.append(json.loads(line.strip()))\n", "    return data\n", "\n", "# Load companies and people\n", "companies = load_jsonl('/content/output/companies.jsonl')\n", "people = load_jsonl('/content/output/people.jsonl')\n", "\n", "print(f\"📊 Loaded {len(companies)} companies and {len(people)} people\")\n", "print(f\"\\n🏢 Sample company: {companies[0]['name']}\")\n", "print(f\"👤 Sample person: {people[0]['name'] if people else 'No people data'}\")"]}, {"cell_type": "markdown", "metadata": {"id": "ksdxJQtmd2XK"}, "source": ["## Step 3: Generate Company Embeddings\n", "\n", "  This step transforms company descriptions into high-dimensional vectors (embeddings)\n", "  using sentence transformers. These embeddings capture the semantic meaning of each\n", "  company's business model, industry focus, and offerings. By converting text to\n", "  numbers, we enable mathematical operations like similarity calculations. The quality\n", "  of these embeddings directly impacts how well we can identify related companies and\n", "  business opportunities.\n", "  \n", "We'll use sentence transformers to create semantic embeddings from company descriptions."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 488, "referenced_widgets": ["af564c9ede624e3ca78d607929a33990", "dd45c7d2fbd64035bd8b2bed64e3105d", "8299d235833244c48f76c6863113b085", "23d963f5f1d445cb80ba7f9d129f25d4", "cbf0822bea5b41a6a8feddb4af2ee7ac", "d881207bc125449caaa22fccbf9360ca", "969f954f20b54467a769d50866961e7c", "71df4d964eca44849a9920416c803612", "54735f0d038145db90bd63927b607f48", "afd44bccb6904692b26b9e053d6a47f3", "96d11a2c702e449799031476fd7ab0f9", "cd9ce7b86ef0404884b066424f015624", "a32e20bbc34d4b52b2ef42053e4fcaf6", "dc3a2f00d58c49c2b331d1473e7644e7", "c53ca07c0b4d47829e4658fda0f5c388", "fe984f1ae3d84c26adc93e016aba4ab1", "3f16a725bd7b4abb8bea8da24c30ea92", "cfdad4190219434ca0c0045eea4f5273", "e40aa1e925c94f0981dd67c2717f8cbf", "df6a2f39b45547e9ba17adca033cf785", "1ade1d7c5a5043cba3d643aeaa1b780d", "59f6da84d5e6465e8d7998d22133e43a", "4928132bed71414aa7b4f0bdf2fcc203", "f01b924c0e6047a69801d07fbf08a4b7", "e190561f198f4f0d90303bd967b92c72", "58923833504a460f909f6446e8dec4ee", "7d00e20ada8a46a89a2554ef831fc228", "3a11abf7227b4416aea98028f8f08be4", "8e54953351f94423b75140e58b14a134", "688172dd486f407cbabc7b1d63de98b8", "469e437e6b044bda83883dff778fa8a5", "4037020e10cc4a16acb8cbfde96464d9", "fa732ec5430b4be58dd45b91cf7e9acb", "b41951c5a9364f578ec45798bef257f2", "221a5aae27ce41f89dfde9c703cd4773", "ef357576aeb24cf8bc45d67ece1f5659", "1b6006c3ae794984a70ca42ebbd834d6", "4359eb00a54e4fb59d0fb115fab65390", "f9c074855b3e493984d572575a6a26dc", "0cad1f8cb11141f3bec24bd7167d4b75", "e34bd1f9ad8a4df08b799812754f473e", "ac84c8e13aed4896970e8608907696a9", "ae5f92c5803649fcb8935f0b430c0086", "19b32b7f074245ce85edadfec42fd3c8", "bbca191bc9b74f859d7d91fe6c3533b2", "aa3c5bcf5e0947758532f34648274572", "89a9c691bfa544308499b1098041d65a", "07c2b46da1fe43299e079d384284b02d", "8921cd4e6ac4420786a6b418070dd085", "a93cf0c5ef97426cba97753f29052fb9", "5addab1004004b42bcd9c5d0fc1db657", "ee613350763d4f288264d2caff15f40a", "e818d840183247848c3088cdb1c900a1", "75910de6ddb14e99b6be5b57389c8e73", "82e8967cbfb64fcd8ba07095330cce6d", "2c69a83341a348d6be987c9910303dec", "0d58ae2061ca43bb8ff9fff98f893821", "10c63cdf413141ab8bfda05b9ca5aace", "685d9c1a57a84aaa80513daa7aceda82", "939f9b8ac9714705a84617e507de8a2f", "5136ef1df7b84acabdedd2f95d7d2e06", "48aa32e63ca24878b5cd218466863274", "eca7f4370b7143f0988297284a5c9137", "f3f3f45fdeed4ceebfb88f08ff2bf3c4", "55165f24721343019afbe4a1c4a83c53", "1f2950ba148b4dbfbfd2a30b5b64aebe", "86384bd507124d7bb348fa5e140b4cf7", "12fc9273eb914555b302dead40be8794", "f304c946daba4aa695cf88e4dd2b86b2", "3032935136074d42aaf5459ffc101552", "41953b5c135a40fd89212576b3d78b36", "0d1b529ef3084c0bae04ec880ccc9768", "1fbefb38397f431b9f5f48da4da10eb1", "a825c331bdac4357bb4b0028ca251e39", "36809b2c33424c078054c244b902c69e", "0f80d6363dd54efdacce7bbf44e6c2ba", "c708f4ca7c46480bba1b789c54319443", "a5a33214229b4f31b78d5f96978a7ecc", "424d9b8e4f3c451e9e5822316417e7d0", "5087c1b1c03e4a278aafcd68bef54154", "61ffe7cbc4c2421994427ddfce6689f0", "a12e6b16c889417886e6384fad789932", "84d7975247f746c48402f3dcbcc9985c", "e6fdcadb7d7f400aa63fc1e613a3dcc4", "eb7c4700e9114cfabc36a3f10e580b43", "2b49618b1ae3460291fccf51d00688ee", "f363fe5110b74ad89c3d4b670ab57b9f", "bcced56d7dbe438c8b1d04037f6b7558", "f571c42002df425281d6f93029df92cd", "7fc8b575514d42bda2704ca37c693059", "e51a514093304c20b9ef4ba8576902e8", "fda4d47309674b97b948f13f242a02ff", "ab15036842cb4181a21acc5220d879e1", "e749bdef3b214d1487ccf2ca4f55e7ad", "9de7ac5f1566419caa76ef80fefbe864", "7dd7c43da93f43f4abaf620fcb77fdff", "68dfb6915eab4e32b43032e795d30c88", "84d3ec9956924edf98ee61a2cd722903", "b03b63087ff94d1f826fa10efad2e287", "478779d52d1342c98d4b58f7793525ca", "af0356140c8f4ea9aa0854302f1a5a16", "c0889a40ae5a4f46bd22b5e2be36893d", "71a876e5968d439691ca885c743cd9ee", "d0d27eb46dd9434d962cd59f63c90ad5", "2d68f023cff04b8892eb7320052b7cf8", "84685055dee943f795f948f0f014be60", "898bf12506ff4f4185499baafc20c474", "39562b44ddad4afeafeccba4c8f3a758", "76077964fc254b0aad053653d915a440", "64b9ab6dbc5448b8ba6999626edc9806", "2ef5efa3119c4036989f967dc83e46ce", "17a45c813b044d22821e7601584a7c03", "04c6d1b69aa344ea8c65383128e87c92", "e331664fedc24fa5b02886700c8eef99", "8da3c08d287943e7b22ff1cceb66b25a", "4d35fd063fa94ff8a6943308b52eb921", "f044b6943e234bedb079cd4b8be543ef", "095756276f494a52851456e2f173021a", "0b0d33c42a0d4bd9a35d9f0e02b333f2", "14a28c11165c472488cc340ce1142189", "e3e1032b5d244aa0a369017042834c26", "a8f4043b1d194d519697c8f460576343", "a3d7bc4c33fb4a0c97b88c5fad55f006", "85bf358df1684c5785707501e1f65b2b", "9984fe8bdb8a4c3b8190a53fa97722d1", "417951d37a4b47c593320c313104e08d", "fb6dcc559b57491e97391f22a06c9a10", "dac947f16e2745d4a8a9d9a75a4e93a2", "40aac98762f14246ac1a03ef27786f33", "e02f702101ce45c08ced253aacb3277a", "026f7ee73bfc4b91a6d2234d5cf20aac", "043f406fce684fc3a0ee4411827edde4"]}, "id": "c2mWlqFVd2XK", "outputId": "4e6dcfac-c96b-447b-ebcb-aec197b6d1d4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🤖 Loading embedding model...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "af564c9ede624e3ca78d607929a33990"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "cd9ce7b86ef0404884b066424f015624"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/10.5k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "4928132bed71414aa7b4f0bdf2fcc203"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b41951c5a9364f578ec45798bef257f2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/612 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "bbca191bc9b74f859d7d91fe6c3533b2"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/90.9M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2c69a83341a348d6be987c9910303dec"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/350 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "86384bd507124d7bb348fa5e140b4cf7"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a5a33214229b4f31b78d5f96978a7ecc"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f571c42002df425281d6f93029df92cd"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "478779d52d1342c98d4b58f7793525ca"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2ef5efa3119c4036989f967dc83e46ce"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["✅ Model loaded!\n", "\n", "🔄 Generating embeddings...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["Batches:   0%|          | 0/1 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a8f4043b1d194d519697c8f460576343"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["✅ Generated embeddings for 5 companies\n"]}], "source": ["# Initialize the embedding model\n", "print(\"🤖 Loading embedding model...\")\n", "model = SentenceTransformer('all-MiniLM-L6-v2')\n", "print(\"✅ Model loaded!\")\n", "\n", "# Create company descriptions for embedding\n", "def create_company_description(company: Dict) -> str:\n", "    \"\"\"Create a rich text description for embedding\"\"\"\n", "    parts = [\n", "        company.get('name', ''),\n", "        company.get('descriptor', ''),\n", "        company.get('about', ''),\n", "        f\"{company.get('followers', 0)} followers\" if company.get('followers') else ''\n", "    ]\n", "    return ' '.join(filter(None, parts))\n", "\n", "# Generate embeddings\n", "print(\"\\n🔄 Generating embeddings...\")\n", "descriptions = [create_company_description(c) for c in companies]\n", "embeddings = model.encode(descriptions, show_progress_bar=True)\n", "\n", "# Add embeddings to company data\n", "for i, company in enumerate(companies):\n", "    company['desc_embed'] = embeddings[i].tolist()\n", "\n", "print(f\"✅ Generated embeddings for {len(companies)} companies\")"]}, {"cell_type": "markdown", "metadata": {"id": "c3EkpEXCd2XL"}, "source": ["## Step 4: Build Company Similarity Graph\n", "\n", "  We now construct a network graph where companies are nodes and weighted edges\n", "  represent their relationships. The similarity scoring combines multiple signals:\n", "  semantic similarity from embeddings, industry alignment bonuses, geographic proximity\n", "  bonuses, and company size compatibility penalties. This multi-factor approach ensures\n", "  the graph reflects real-world B2B relationship potential, not just textual similarity.\n", "   The resulting graph reveals clusters of related companies and potential partnership\n", "  opportunities.\n", "  \n", "  Calculate similarity scores between companies and apply bonuses for matching industries/locations."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "pHyu7gjMd2XL"}, "outputs": [], "source": ["def extract_industry(descriptor: str) -> Optional[str]:\n", "    \"\"\"Extract industry from descriptor (e.g., 'Insurance • Singapore')\"\"\"\n", "    if not descriptor or '•' not in descriptor:\n", "        return None\n", "    return descriptor.split('•')[0].strip()\n", "\n", "def extract_location(descriptor: str) -> Optional[str]:\n", "    \"\"\"Extract location from descriptor\"\"\"\n", "    if not descriptor or '•' not in descriptor:\n", "        return None\n", "    return descriptor.split('•')[-1].strip()\n", "\n", "def calculate_similarity_score(c1: Dict, c2: Dict, embeddings: np.n<PERSON><PERSON>,\n", "                            idx1: int, idx2: int) -> float:\n", "    \"\"\"Calculate weighted similarity between two companies\n", "\n", "    This function combines multiple signals to determine how similar two companies are:\n", "    1. Semantic similarity (from embeddings)\n", "    2. Industry alignment\n", "    3. Geographic proximity\n", "    4. Company size compatibility\n", "    \"\"\"\n", "\n", "    # Base cosine similarity (0 to 1)\n", "    # This captures semantic similarity from company descriptions\n", "    # Higher values mean more similar business models/offerings\n", "    base_sim = cosine_similarity([embeddings[idx1]], [embeddings[idx2]])[0][0]\n", "\n", "    # Start with base similarity as our weight\n", "    weight = base_sim\n", "\n", "    # Industry bonus (+0.10)\n", "    # Companies in the same industry are more likely to:\n", "    # - Face similar challenges\n", "    # - Need complementary services\n", "    # - Understand each other's business context\n", "    # Example: Two \"Insurance\" companies get a bonus even if their descriptions differ\n", "    ind1 = extract_industry(c1.get('descriptor', ''))\n", "    ind2 = extract_industry(c2.get('descriptor', ''))\n", "    if ind1 and ind2 and ind1.lower() == ind2.lower():\n", "        weight += INDUSTRY_WEIGHT_BONUS  # +0.10\n", "\n", "    # Geographic bonus (+0.05)\n", "    # Companies in the same location benefit from:\n", "    # - Easier in-person meetings\n", "    # - Similar regulatory environment\n", "    # - Local partnership opportunities\n", "    # - Shared timezone for collaboration\n", "    loc1 = extract_location(c1.get('descriptor', ''))\n", "    loc2 = extract_location(c2.get('descriptor', ''))\n", "    if loc1 and loc2 and loc1.lower() == loc2.lower():\n", "        weight += GEO_WEIGHT_BONUS  # +0.05\n", "\n", "    # Follower ratio penalty (scales weight by 0.5 to 1.0)\n", "    # This addresses company size compatibility:\n", "    # - Similar-sized companies often have comparable resources\n", "    # - Prevents unrealistic pairings (e.g., 10-person startup with Microsoft)\n", "    # - Rat<PERSON> close to 1.0 = similar size (no penalty)\n", "    # - Ratio close to 0.0 = very different sizes (50% penalty)\n", "    f1 = c1.get('followers', 1) or 1  # Avoid division by zero\n", "    f2 = c2.get('followers', 1) or 1\n", "    ratio = min(f1, f2) / max(f1, f2)  # Always between 0 and 1\n", "\n", "    # Scale the penalty: at worst (ratio=0), multiply by 0.5\n", "    # at best (ratio=1), multiply by 1.0 (no penalty)\n", "    weight *= (0.5 + 0.5 * ratio)\n", "\n", "    # Example calculation:\n", "    # - Base similarity: 0.7\n", "    # - Same industry: +0.1 → 0.8\n", "    # - Same location: +0.05 → 0.85\n", "    # - Size ratio 0.2: × 0.6 → 0.51 final score\n", "\n", "    # Cap at 1.0 to maintain valid probability range\n", "    return min(weight, 1.0)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UFEIzHEbd2XL", "outputId": "85525cdc-4303-4790-8089-63a4d653aa74"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🕸️ Building company similarity graph...\n", "✅ Graph built with 5 nodes and 7 edges\n", "📊 Average connections per company: 2.8\n"]}], "source": ["# Build the similarity graph\n", "print(\"🕸️ Building company similarity graph...\")\n", "\n", "nodes = []\n", "edges = []\n", "\n", "# Create nodes\n", "for company in companies:\n", "    nodes.append({\n", "        'id': company['handle'],\n", "        'name': company['name'],\n", "        'industry': extract_industry(company.get('descriptor', '')),\n", "        'location': extract_location(company.get('descriptor', '')),\n", "        'followers': company.get('followers', 0),\n", "        'about': company.get('about', ''),\n", "        'handle': company['handle'],\n", "        'desc_embed': company['desc_embed']\n", "    })\n", "\n", "# Create edges (similarities above threshold)\n", "for i in range(len(companies)):\n", "    for j in range(i + 1, len(companies)):\n", "        score = calculate_similarity_score(\n", "            companies[i], companies[j], embeddings, i, j\n", "        )\n", "\n", "        if score >= SIMILARITY_THRESHOLD:\n", "            edges.append({\n", "                'source': companies[i]['handle'],\n", "                'target': companies[j]['handle'],\n", "                'weight': float(score)\n", "            })\n", "\n", "# Create graph data structure\n", "graph_data = {\n", "    'nodes': nodes,\n", "    'edges': edges,\n", "    'metadata': {\n", "        'created_at': datetime.now().isoformat(),\n", "        'total_companies': len(companies),\n", "        'total_connections': len(edges),\n", "        'similarity_threshold': SIMILARITY_THRESHOLD\n", "    }\n", "}\n", "\n", "# Save graph\n", "with open('/content/output/company_graph.json', 'w') as f:\n", "    json.dump(graph_data, f, indent=2)\n", "\n", "print(f\"✅ Graph built with {len(nodes)} nodes and {len(edges)} edges\")\n", "print(f\"📊 Average connections per company: {len(edges) * 2 / len(nodes):.1f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "hfUTMafOd2XL"}, "source": ["## Step 5: Infer Organizational Charts with LLM\n", "\n", "  This step leverages Large Language Models to analyze employee titles and infer\n", "  organizational hierarchies. For each company, we send employee data to the LLM with a\n", "  structured prompt requesting org chart inference, reporting relationships, and\n", "  decision-making scores. The LLM uses its training on corporate structures to identify\n", "  C-level executives, VPs, directors, and their likely reporting chains. This automated\n", "  inference scales what would otherwise require manual research for each company.\n", "  \n", "  Use LLM to analyze job titles and infer reporting structures."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0sOhkTNsd2XL", "outputId": "cfdedf0b-5311-4349-c68c-13a7b2242530"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Using openai/gpt-4.1\n"]}], "source": ["# Configure LLM\n", "try:\n", "    # Try to get API key from Colab secrets\n", "    provider = \"OPENAI\"\n", "    model = \"gpt-4.1\"\n", "    api_key = userdata.get(f'{provider}_API_KEY')\n", "    litellm.api_key = api_key\n", "    LLM_MODEL = f\"{provider.lower()}/{model}\"\n", "    print(\"✅ Using \" + LLM_MODEL)\n", "except:\n", "    print(f\"⚠️ No {provider} API key found. Please add {provider}_API_KEY to Colab secrets.\")\n", "    api_key = input(\"Enter your API key (GEMINI, OPENAI, ...): \")\n", "    provider = input(\"Enter provider (GEMINI, OPENAI, ...): \").lower()\n", "    model = input(\"Enter model (gpt-4.1, gpt-4o-mini): \").lower()\n", "    LLM_MODEL = f\"{provider.lower()}/{model}\"\n", "    litellm.api_key = api_key"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "n-uKhazMd2XL"}, "outputs": [], "source": ["# Org chart inference prompt template\n", "ORG_CHART_PROMPT = \"\"\"Analyze these LinkedIn profiles and infer the organizational structure.\n", "\n", "Company: {company_name}\n", "Employees:\n", "````\n", "{employees_text}\n", "````\n", "\n", "Create a hierarchical org chart with:\n", "1. Reporting relationships (who reports to whom)\n", "2. Decision-making score (0.0-1.0) based on seniority and title\n", "3. Department classification\n", "\n", "Return ONLY valid JSON in this format:\n", "{{\n", "  \"nodes\": [\n", "    {{\n", "      \"id\": \"profile_url\",\n", "      \"name\": \"person name\",\n", "      \"title\": \"job title\",\n", "      \"dept\": \"department\",\n", "      \"decision_score\": 0.0-1.0,\n", "      \"title_level\": \"C-<PERSON>|VP|Director|Manager|IC\"\n", "    }}\n", "  ],\n", "  \"edges\": [\n", "    {{\"source\": \"manager_profile_url\", \"target\": \"report_profile_url\"}}\n", "  ]\n", "}}\n", "\"\"\"\n", "\n", "def infer_org_chart(company: Dict, employees: List[Dict]) -> Optional[Dict]:\n", "    \"\"\"Use LLM to infer organizational structure\"\"\"\n", "    if not employees:\n", "        return None\n", "\n", "    # Format employee data\n", "    emp_lines = []\n", "    for emp in employees[:50]:  # Limit to 50 for token constraints\n", "        emp_lines.append(\n", "            f\"- {emp.get('name', 'Unknown')} | \"\n", "            f\"{emp.get('headline', 'No title')} | \"\n", "            f\"URL: {emp.get('profile_url', 'N/A')}\"\n", "        )\n", "\n", "    prompt = ORG_CHART_PROMPT.format(\n", "        company_name=company['name'],\n", "        employees_text='\\n'.join(emp_lines)\n", "    )\n", "\n", "    try:\n", "        response = litellm.completion(\n", "            model=LLM_MODEL,\n", "            messages=[{\"role\": \"user\", \"content\": prompt}],\n", "            temperature=0.3,\n", "            response_format={\"type\": \"json_object\"}\n", "        )\n", "\n", "        result = json.loads(response.choices[0].message.content)\n", "\n", "        # Add metadata\n", "        result['meta'] = {\n", "            'company': company['name'],\n", "            'company_handle': company['handle'],\n", "            'total_analyzed': len(employees),\n", "            'created_at': datetime.now().isoformat()\n", "        }\n", "\n", "        return result\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Error inferring org chart for {company['name']}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "D1cRktRNd2XL", "outputId": "cba44422-fa74-46a6-d1a2-1d44b431c788"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🏢 Inferring organizational structures...\n", "\n", "Processing 1/5: Posify...\n", "  ✅ Found 6 decision makers\n", "Processing 2/5: Omniware...\n", "  ✅ Found 4 decision makers\n", "Processing 3/5: CloudServe...\n", "  ✅ Found 4 decision makers\n", "Processing 4/5: TableIQ...\n", "  ✅ Found 4 decision makers\n", "Processing 5/5: PayPulse...\n", "  ✅ Found 6 decision makers\n", "\n", "✅ Processed 5 companies\n", "🎯 Found 24 total decision makers\n"]}], "source": ["# Process organizational charts for each company\n", "print(\"🏢 Inferring organizational structures...\\n\")\n", "\n", "# Group people by company\n", "people_by_company = defaultdict(list)\n", "for person in people:\n", "    company_handle = person.get('company_handle', '')\n", "    if company_handle:\n", "        people_by_company[company_handle].append(person)\n", "\n", "# Process each company\n", "org_charts = {}\n", "decision_makers = []\n", "\n", "for i, company in enumerate(companies):\n", "    print(f\"Processing {i+1}/{len(companies)}: {company['name']}...\")\n", "\n", "    company_people = people_by_company.get(company['handle'], [])\n", "\n", "    if not company_people:\n", "        print(f\"  ⚠️ No employees found\")\n", "        continue\n", "\n", "    # Infer org chart\n", "    org_chart = infer_org_chart(company, company_people)\n", "\n", "    if org_chart:\n", "        # Save org chart\n", "        safe_handle = company['handle'].replace('/', '_')\n", "        filename = f'/content/output/org_chart_{safe_handle}.json'\n", "\n", "        with open(filename, 'w') as f:\n", "            json.dump(org_chart, f, indent=2)\n", "\n", "        org_charts[company['handle']] = org_chart\n", "\n", "        # Extract decision makers\n", "        for node in org_chart.get('nodes', []):\n", "            if node.get('decision_score', 0) >= DECISION_THRESHOLD:\n", "                # Find original person data\n", "                person_data = next(\n", "                    (p for p in company_people if p.get('profile_url') == node['id']),\n", "                    {}\n", "                )\n", "\n", "                decision_makers.append({\n", "                    'name': node['name'],\n", "                    'title': node['title'],\n", "                    'company': company['name'],\n", "                    'company_handle': company['handle'],\n", "                    'decision_score': node['decision_score'],\n", "                    'title_level': node.get('title_level', 'Unknown'),\n", "                    'dept': node.get('dept', 'Unknown'),\n", "                    'profile_url': node['id'],\n", "                    'avatar_url': person_data.get('avatar_url', ''),\n", "                    'connection_degree': person_data.get('connection_degree', ''),\n", "                    'followers': person_data.get('followers', ''),\n", "                    'yoe_current': node.get('yoe_current', 0),\n", "                    'connection_count': person_data.get('connection_count', 0)\n", "                })\n", "\n", "        print(f\"  ✅ Found {len([n for n in org_chart.get('nodes', []) if n.get('decision_score', 0) >= DECISION_THRESHOLD])} decision makers\")\n", "    else:\n", "        print(f\"  ❌ Failed to generate org chart\")\n", "\n", "print(f\"\\n✅ Processed {len(org_charts)} companies\")\n", "print(f\"🎯 Found {len(decision_makers)} total decision makers\")"]}, {"cell_type": "markdown", "metadata": {"id": "5aNPZK1bd2XL"}, "source": ["## Step 6: Export Decision Makers\n", "\n", "  We extract and rank individuals with high decision-making potential based on their\n", "  inferred organizational position. The system filters employees by decision score\n", "  (typically 0.5 or higher), enriches their profiles with company context, and exports\n", "  them to a CSV file. This creates a prioritized contact list for B2B sales teams,\n", "  focusing efforts on individuals most likely to influence purchasing decisions."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Pd7oBB2xd2XL", "outputId": "bc659c08-2c64-414c-a345-9d4a5004373c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📊 Top 10 Decision Makers:\n", "================================================================================\n", "<PERSON><PERSON>                    | CTO                                      | Score: 1.00\n", "  Company: Posify\n", "  Level: C-Level | Dept: Engineering\n", "--------------------------------------------------------------------------------\n", "<PERSON>                       | CTO                                      | Score: 1.00\n", "  Company: Posify\n", "  Level: C-Level | Dept: Engineering\n", "--------------------------------------------------------------------------------\n", "<PERSON>                       | CTO                                      | Score: 1.00\n", "  Company: Omniware\n", "  Level: C-Level | Dept: Engineering\n", "--------------------------------------------------------------------------------\n", "<PERSON>                  | CTO                                      | Score: 1.00\n", "  Company: CloudServe\n", "  Level: C-Level | Dept: Technology\n", "--------------------------------------------------------------------------------\n", "<PERSON>                        | CTO                                      | Score: 1.00\n", "  Company: CloudServe\n", "  Level: C-Level | Dept: Technology\n", "--------------------------------------------------------------------------------\n", "<PERSON>                     | CTO                                      | Score: 1.00\n", "  Company: TableIQ\n", "  Level: C-Level | Dept: Engineering\n", "--------------------------------------------------------------------------------\n", "<PERSON>                    | VP Product                               | Score: 0.90\n", "  Company: PayPulse\n", "  Level: VP | Dept: Product\n", "--------------------------------------------------------------------------------\n", "<PERSON>                     | VP Product                               | Score: 0.90\n", "  Company: Posify\n", "  Level: VP | Dept: Product\n", "--------------------------------------------------------------------------------\n", "<PERSON>                    | VP Product                               | Score: 0.85\n", "  Company: CloudServe\n", "  Level: VP | Dept: Product\n", "--------------------------------------------------------------------------------\n", "<PERSON>                        | Head of Engineering                      | Score: 0.85\n", "  Company: TableIQ\n", "  Level: VP | Dept: Engineering\n", "--------------------------------------------------------------------------------\n"]}], "source": ["# Create decision makers DataFrame and export to CSV\n", "if decision_makers:\n", "    df_decision_makers = pd.DataFrame(decision_makers)\n", "\n", "    # Sort by decision score\n", "    df_decision_makers = df_decision_makers.sort_values(\n", "        'decision_score', ascending=False\n", "    )\n", "\n", "    # Save to CSV\n", "    df_decision_makers.to_csv('/content/output/decision_makers.csv', index=False)\n", "\n", "    print(\"📊 Top 10 Decision Makers:\")\n", "    print(\"=\" * 80)\n", "\n", "    for _, person in df_decision_makers.head(10).iterrows():\n", "        print(f\"{person['name']:<30} | {person['title']:<40} | Score: {person['decision_score']:.2f}\")\n", "        print(f\"  Company: {person['company']}\")\n", "        print(f\"  Level: {person['title_level']} | Dept: {person['dept']}\")\n", "        print(\"-\" * 80)\n", "else:\n", "    print(\"⚠️ No decision makers found\")"]}, {"cell_type": "markdown", "metadata": {"id": "Fi-3KX8Id2XL"}, "source": ["## Step 7: Generate Interactive Visualization\n", "\n", "  This step assembles all components into an interactive web application. We copy the\n", "  HTML template and JavaScript files, ensure all generated data files (company graph,\n", "  org charts, people data) are in place, and prepare the visualization environment. The\n", "  template includes a network graph viewer (vis.js), organizational chart displays, and\n", "  an AI chat interface for querying the knowledge base. This creates a complete,\n", "  self-contained dashboard."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Iu4cOt_jd2XL", "outputId": "3dcf7652-180e-479e-81c2-e85b6c1a6880"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["cp: '/content/output/people.jsonl' and '/content/output/people.jsonl' are the same file\n", "✅ Visualization files prepared!\n", "\n", "📁 Output directory contents:\n", "total 168\n", "drwxr-xr-x 2 <USER> <GROUP>  4096 Jun  5 05:15 .\n", "drwxr-xr-x 1 <USER> <GROUP>  4096 Jun  5 05:06 ..\n", "-rw-r--r-- 1 <USER> <GROUP>  1652 Jun  5 05:15 ai.js\n", "-rw-r--r-- 1 <USER> <GROUP>  1341 Jun  5 05:06 companies.jsonl\n", "-rw-r--r-- 1 <USER> <GROUP> 59970 Jun  5 05:08 company_graph.json\n", "-rw-r--r-- 1 <USER> <GROUP>  5190 Jun  5 05:15 decision_makers.csv\n", "-rw-r--r-- 1 <USER> <GROUP> 51566 Jun  5 05:15 graph_view.html\n", "-rw-r--r-- 1 <USER> <GROUP>  1560 Jun  5 05:14 org_chart__company_cloudserve_.json\n", "-rw-r--r-- 1 <USER> <GROUP>  1463 Jun  5 05:14 org_chart__company_omniware_.json\n", "-rw-r--r-- 1 <USER> <GROUP>  2201 Jun  5 05:14 org_chart__company_paypulse_.json\n", "-rw-r--r-- 1 <USER> <GROUP>  2262 Jun  5 05:14 org_chart__company_posify_.json\n", "-rw-r--r-- 1 <USER> <GROUP>  1458 Jun  5 05:14 org_chart__company_tableiq_.json\n", "-rw-r--r-- 1 <USER> <GROUP>  8327 Jun  5 05:06 people.jsonl\n"]}], "source": ["# Copy template files and AI.js\n", "!cp /content/templates/graph_view_template.html /content/output/graph_view.html\n", "!cp /content/templates/ai.js /content/output/\n", "\n", "# Also copy the people.jsonl for the chat context\n", "!cp /content/output/people.jsonl /content/output/\n", "\n", "print(\"✅ Visualization files prepared!\")\n", "print(\"\\n📁 Output directory contents:\")\n", "!ls -la /content/output/"]}, {"cell_type": "markdown", "metadata": {"id": "KQklZjdxd2XL"}, "source": ["## Step 8: Launch Web Server and Display\n", "\n", "  Finally, we start a local HTTP server to serve the visualization files and display\n", "  them within Colab using an iframe. The server runs in the background on port 8000, and\n", "   Colab's proxy system allows secure access to the dashboard. Users can interact with\n", "  the graph, click on companies to view organizational structures, and use the AI chat\n", "  to query the data. This step brings together all previous work into a live,\n", "  interactive B2B intelligence tool."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "M-9XKPZEd2XL", "outputId": "1553d2b8-c972-4818-9073-7ab1febcb773"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🌐 Web server started on port 8000\n", "📊 Loading visualization...\n"]}], "source": ["# Start a simple HTTP server in the background\n", "import subprocess\n", "import time\n", "\n", "# Kill any existing server on port 8000\n", "!kill -9 $(lsof -t -i:8000) 2>/dev/null || true\n", "\n", "# Start the server\n", "server_process = subprocess.Popen(\n", "    ['python', '-m', 'http.server', '8000', '--directory', '/content/output'],\n", "    stdout=subprocess.DEVNULL,\n", "    stderr=subprocess.DEVNULL\n", ")\n", "\n", "# Wait for server to start\n", "time.sleep(2)\n", "\n", "print(\"🌐 Web server started on port 8000\")\n", "print(\"📊 Loading visualization...\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 978}, "id": "vHTSBOzMd2XL", "outputId": "f7bf2a9a-e5cb-44aa-baf5-c00c5c6867dc"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🎉 Your LinkedIn Insights Dashboard is ready!\n", "\n", "📌 Instructions:\n", "1. The graph shows company relationships\n", "2. Click on a company to see its org chart\n", "3. Use the chat button to ask questions about the data\n", "4. Don't forget to set your OpenAI API key in Settings for chat to work\n", "\n", "⚠️ Note: If the visualization doesn't load, refresh this cell\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["<IPython.lib.display.IFrame at 0x7ad5c0d1df50>"], "text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"800\"\n", "            src=\"https://8000-m-s-ecy4y99edwjh-b.us-west1-1.prod.colab.dev/graph_view.html\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "]}, "metadata": {}, "execution_count": 19}], "source": ["# Display the visualization in an iframe\n", "from IPython.display import IFrame\n", "\n", "# Note: In Colab, we need to use the proxy URL\n", "from google.colab.output import eval_js\n", "proxy_url = eval_js(\"google.colab.kernel.proxyPort(8000)\")\n", "\n", "print(\"🎉 Your LinkedIn Insights Dashboard is ready!\")\n", "print(\"\\n📌 Instructions:\")\n", "print(\"1. The graph shows company relationships\")\n", "print(\"2. Click on a company to see its org chart\")\n", "print(\"3. Use the chat button to ask questions about the data\")\n", "print(\"4. Don't forget to set your OpenAI API key in Settings for chat to work\")\n", "print(\"\\n⚠️ Note: If the visualization doesn't load, refresh this cell\")\n", "\n", "# Display the iframe\n", "IFrame(src=proxy_url + \"/graph_view.html\", width='100%', height='800')"]}, {"cell_type": "code", "source": ["# Get public URL for the server\n", "from google.colab.output import eval_js\n", "public_url = eval_js(\"google.colab.kernel.proxyPort(8000)\")\n", "\n", "print(f\"🌐 Public URL: {public_url}\")\n", "print(\"\\n📱 Share this URL to access your dashboard from anywhere!\")\n", "print(\"⚠️ Note: This URL is temporary and will expire when the Colab session ends\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 86}, "id": "V_y0iwsDiO6w", "outputId": "c56f6e5b-8c93-44cf-b57e-61866bb284f6"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🌐 Public URL: https://8000-m-s-ecy4y99edwjh-b.us-west1-1.prod.colab.dev\n", "\n", "📱 Share this URL to access your dashboard from anywhere!\n", "⚠️ Note: This URL is temporary and will expire when the Colab session ends\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "b4tt60PTd2XL"}, "source": ["## 🎯 Summary & Next Steps\n", "\n", "Congratulations! You've built a complete B2B intelligence system that:\n", "\n", "✅ **Analyzed** company similarities using AI embeddings  \n", "✅ **Inferred** organizational structures with LLM  \n", "✅ **Identified** key decision makers  \n", "✅ **Visualized** everything in an interactive dashboard  \n", "\n", "### Download Your Results\n", "\n", "Run the cell below to download all generated files:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JLAu-ak2d2XM"}, "outputs": [], "source": ["# Create a zip file with all outputs\n", "!cd /content/output && zip -r linkedin_insights.zip *.json *.csv *.html *.js *.jsonl\n", "\n", "# Download the zip file\n", "from google.colab import files\n", "files.download('/content/output/linkedin_insights.zip')\n", "\n", "print(\"📦 All files packaged and ready for download!\")"]}, {"cell_type": "markdown", "metadata": {"id": "Sek8o_S8d2XM"}, "source": ["## 🚀 What's Next?\n", "\n", "### Enhance Your System:\n", "1. **Add more data sources** - Combine with CRM, news, social media\n", "2. **Improve scoring** - Use more sophisticated algorithms\n", "3. **Track changes** - Monitor company/people updates over time\n", "4. **Export to CRM** - Integrate with Salesforce, HubSpot, etc.\n", "\n", "### Production Deployment:\n", "1. **Host the dashboard** - Deploy to Vercel, Netlify, or AWS\n", "2. **Add authentication** - Secure your intelligence data\n", "3. **Schedule updates** - Automate data refreshes\n", "4. **Scale the pipeline** - Process thousands of companies\n", "\n", "## Connect & Learn More\n", "\n", "- 🐙 **GitHub**: [github.com/unclecode/crawl4ai](https://github.com/unclecode/crawl4ai)\n", "- 🐦 **Follow on X**: [@unclecode](https://twitter.com/unclecode)\n", "- 💬 **Join our Discord**: [discord.gg/gpPZZgzRAP](https://discord.gg/gpPZZgzRAP)\n", "\n", "Thank you for joining this workshop! 🙏\n", "\n", "---\n", "\n", "Live Long and Build Intelligent Systems 🖖"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "colab": {"provenance": []}, "widgets": {"application/vnd.jupyter.widget-state+json": {"af564c9ede624e3ca78d607929a33990": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_dd45c7d2fbd64035bd8b2bed64e3105d", "IPY_MODEL_8299d235833244c48f76c6863113b085", "IPY_MODEL_23d963f5f1d445cb80ba7f9d129f25d4"], "layout": "IPY_MODEL_cbf0822bea5b41a6a8feddb4af2ee7ac"}}, "dd45c7d2fbd64035bd8b2bed64e3105d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d881207bc125449caaa22fccbf9360ca", "placeholder": "​", "style": "IPY_MODEL_969f954f20b54467a769d50866961e7c", "value": "modules.json: 100%"}}, "8299d235833244c48f76c6863113b085": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_71df4d964eca44849a9920416c803612", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_54735f0d038145db90bd63927b607f48", "value": 349}}, "23d963f5f1d445cb80ba7f9d129f25d4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_afd44bccb6904692b26b9e053d6a47f3", "placeholder": "​", "style": "IPY_MODEL_96d11a2c702e449799031476fd7ab0f9", "value": " 349/349 [00:00&lt;00:00, 26.4kB/s]"}}, "cbf0822bea5b41a6a8feddb4af2ee7ac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d881207bc125449caaa22fccbf9360ca": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "969f954f20b54467a769d50866961e7c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "71df4d964eca44849a9920416c803612": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "54735f0d038145db90bd63927b607f48": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "afd44bccb6904692b26b9e053d6a47f3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "96d11a2c702e449799031476fd7ab0f9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cd9ce7b86ef0404884b066424f015624": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a32e20bbc34d4b52b2ef42053e4fcaf6", "IPY_MODEL_dc3a2f00d58c49c2b331d1473e7644e7", "IPY_MODEL_c53ca07c0b4d47829e4658fda0f5c388"], "layout": "IPY_MODEL_fe984f1ae3d84c26adc93e016aba4ab1"}}, "a32e20bbc34d4b52b2ef42053e4fcaf6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f16a725bd7b4abb8bea8da24c30ea92", "placeholder": "​", "style": "IPY_MODEL_cfdad4190219434ca0c0045eea4f5273", "value": "config_sentence_transformers.json: 100%"}}, "dc3a2f00d58c49c2b331d1473e7644e7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e40aa1e925c94f0981dd67c2717f8cbf", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_df6a2f39b45547e9ba17adca033cf785", "value": 116}}, "c53ca07c0b4d47829e4658fda0f5c388": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1ade1d7c5a5043cba3d643aeaa1b780d", "placeholder": "​", "style": "IPY_MODEL_59f6da84d5e6465e8d7998d22133e43a", "value": " 116/116 [00:00&lt;00:00, 10.6kB/s]"}}, "fe984f1ae3d84c26adc93e016aba4ab1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3f16a725bd7b4abb8bea8da24c30ea92": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cfdad4190219434ca0c0045eea4f5273": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e40aa1e925c94f0981dd67c2717f8cbf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "df6a2f39b45547e9ba17adca033cf785": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1ade1d7c5a5043cba3d643aeaa1b780d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59f6da84d5e6465e8d7998d22133e43a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4928132bed71414aa7b4f0bdf2fcc203": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f01b924c0e6047a69801d07fbf08a4b7", "IPY_MODEL_e190561f198f4f0d90303bd967b92c72", "IPY_MODEL_58923833504a460f909f6446e8dec4ee"], "layout": "IPY_MODEL_7d00e20ada8a46a89a2554ef831fc228"}}, "f01b924c0e6047a69801d07fbf08a4b7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3a11abf7227b4416aea98028f8f08be4", "placeholder": "​", "style": "IPY_MODEL_8e54953351f94423b75140e58b14a134", "value": "README.md: 100%"}}, "e190561f198f4f0d90303bd967b92c72": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_688172dd486f407cbabc7b1d63de98b8", "max": 10454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_469e437e6b044bda83883dff778fa8a5", "value": 10454}}, "58923833504a460f909f6446e8dec4ee": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4037020e10cc4a16acb8cbfde96464d9", "placeholder": "​", "style": "IPY_MODEL_fa732ec5430b4be58dd45b91cf7e9acb", "value": " 10.5k/10.5k [00:00&lt;00:00, 789kB/s]"}}, "7d00e20ada8a46a89a2554ef831fc228": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a11abf7227b4416aea98028f8f08be4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8e54953351f94423b75140e58b14a134": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "688172dd486f407cbabc7b1d63de98b8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "469e437e6b044bda83883dff778fa8a5": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4037020e10cc4a16acb8cbfde96464d9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fa732ec5430b4be58dd45b91cf7e9acb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b41951c5a9364f578ec45798bef257f2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_221a5aae27ce41f89dfde9c703cd4773", "IPY_MODEL_ef357576aeb24cf8bc45d67ece1f5659", "IPY_MODEL_1b6006c3ae794984a70ca42ebbd834d6"], "layout": "IPY_MODEL_4359eb00a54e4fb59d0fb115fab65390"}}, "221a5aae27ce41f89dfde9c703cd4773": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f9c074855b3e493984d572575a6a26dc", "placeholder": "​", "style": "IPY_MODEL_0cad1f8cb11141f3bec24bd7167d4b75", "value": "sentence_bert_config.json: 100%"}}, "ef357576aeb24cf8bc45d67ece1f5659": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e34bd1f9ad8a4df08b799812754f473e", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ac84c8e13aed4896970e8608907696a9", "value": 53}}, "1b6006c3ae794984a70ca42ebbd834d6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ae5f92c5803649fcb8935f0b430c0086", "placeholder": "​", "style": "IPY_MODEL_19b32b7f074245ce85edadfec42fd3c8", "value": " 53.0/53.0 [00:00&lt;00:00, 3.28kB/s]"}}, "4359eb00a54e4fb59d0fb115fab65390": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f9c074855b3e493984d572575a6a26dc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0cad1f8cb11141f3bec24bd7167d4b75": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e34bd1f9ad8a4df08b799812754f473e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ac84c8e13aed4896970e8608907696a9": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ae5f92c5803649fcb8935f0b430c0086": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "19b32b7f074245ce85edadfec42fd3c8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bbca191bc9b74f859d7d91fe6c3533b2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_aa3c5bcf5e0947758532f34648274572", "IPY_MODEL_89a9c691bfa544308499b1098041d65a", "IPY_MODEL_07c2b46da1fe43299e079d384284b02d"], "layout": "IPY_MODEL_8921cd4e6ac4420786a6b418070dd085"}}, "aa3c5bcf5e0947758532f34648274572": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a93cf0c5ef97426cba97753f29052fb9", "placeholder": "​", "style": "IPY_MODEL_5addab1004004b42bcd9c5d0fc1db657", "value": "config.json: 100%"}}, "89a9c691bfa544308499b1098041d65a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ee613350763d4f288264d2caff15f40a", "max": 612, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e818d840183247848c3088cdb1c900a1", "value": 612}}, "07c2b46da1fe43299e079d384284b02d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_75910de6ddb14e99b6be5b57389c8e73", "placeholder": "​", "style": "IPY_MODEL_82e8967cbfb64fcd8ba07095330cce6d", "value": " 612/612 [00:00&lt;00:00, 55.2kB/s]"}}, "8921cd4e6ac4420786a6b418070dd085": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a93cf0c5ef97426cba97753f29052fb9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5addab1004004b42bcd9c5d0fc1db657": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee613350763d4f288264d2caff15f40a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e818d840183247848c3088cdb1c900a1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "75910de6ddb14e99b6be5b57389c8e73": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "82e8967cbfb64fcd8ba07095330cce6d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2c69a83341a348d6be987c9910303dec": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0d58ae2061ca43bb8ff9fff98f893821", "IPY_MODEL_10c63cdf413141ab8bfda05b9ca5aace", "IPY_MODEL_685d9c1a57a84aaa80513daa7aceda82"], "layout": "IPY_MODEL_939f9b8ac9714705a84617e507de8a2f"}}, "0d58ae2061ca43bb8ff9fff98f893821": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5136ef1df7b84acabdedd2f95d7d2e06", "placeholder": "​", "style": "IPY_MODEL_48aa32e63ca24878b5cd218466863274", "value": "model.safetensors: 100%"}}, "10c63cdf413141ab8bfda05b9ca5aace": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eca7f4370b7143f0988297284a5c9137", "max": 90868376, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f3f3f45fdeed4ceebfb88f08ff2bf3c4", "value": 90868376}}, "685d9c1a57a84aaa80513daa7aceda82": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_55165f24721343019afbe4a1c4a83c53", "placeholder": "​", "style": "IPY_MODEL_1f2950ba148b4dbfbfd2a30b5b64aebe", "value": " 90.9M/90.9M [00:01&lt;00:00, 77.1MB/s]"}}, "939f9b8ac9714705a84617e507de8a2f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5136ef1df7b84acabdedd2f95d7d2e06": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "48aa32e63ca24878b5cd218466863274": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "eca7f4370b7143f0988297284a5c9137": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f3f3f45fdeed4ceebfb88f08ff2bf3c4": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "55165f24721343019afbe4a1c4a83c53": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1f2950ba148b4dbfbfd2a30b5b64aebe": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "86384bd507124d7bb348fa5e140b4cf7": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_12fc9273eb914555b302dead40be8794", "IPY_MODEL_f304c946daba4aa695cf88e4dd2b86b2", "IPY_MODEL_3032935136074d42aaf5459ffc101552"], "layout": "IPY_MODEL_41953b5c135a40fd89212576b3d78b36"}}, "12fc9273eb914555b302dead40be8794": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0d1b529ef3084c0bae04ec880ccc9768", "placeholder": "​", "style": "IPY_MODEL_1fbefb38397f431b9f5f48da4da10eb1", "value": "tokenizer_config.json: 100%"}}, "f304c946daba4aa695cf88e4dd2b86b2": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a825c331bdac4357bb4b0028ca251e39", "max": 350, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_36809b2c33424c078054c244b902c69e", "value": 350}}, "3032935136074d42aaf5459ffc101552": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0f80d6363dd54efdacce7bbf44e6c2ba", "placeholder": "​", "style": "IPY_MODEL_c708f4ca7c46480bba1b789c54319443", "value": " 350/350 [00:00&lt;00:00, 16.8kB/s]"}}, "41953b5c135a40fd89212576b3d78b36": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d1b529ef3084c0bae04ec880ccc9768": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1fbefb38397f431b9f5f48da4da10eb1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a825c331bdac4357bb4b0028ca251e39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "36809b2c33424c078054c244b902c69e": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0f80d6363dd54efdacce7bbf44e6c2ba": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c708f4ca7c46480bba1b789c54319443": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a5a33214229b4f31b78d5f96978a7ecc": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_424d9b8e4f3c451e9e5822316417e7d0", "IPY_MODEL_5087c1b1c03e4a278aafcd68bef54154", "IPY_MODEL_61ffe7cbc4c2421994427ddfce6689f0"], "layout": "IPY_MODEL_a12e6b16c889417886e6384fad789932"}}, "424d9b8e4f3c451e9e5822316417e7d0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_84d7975247f746c48402f3dcbcc9985c", "placeholder": "​", "style": "IPY_MODEL_e6fdcadb7d7f400aa63fc1e613a3dcc4", "value": "vocab.txt: 100%"}}, "5087c1b1c03e4a278aafcd68bef54154": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_eb7c4700e9114cfabc36a3f10e580b43", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2b49618b1ae3460291fccf51d00688ee", "value": 231508}}, "61ffe7cbc4c2421994427ddfce6689f0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f363fe5110b74ad89c3d4b670ab57b9f", "placeholder": "​", "style": "IPY_MODEL_bcced56d7dbe438c8b1d04037f6b7558", "value": " 232k/232k [00:00&lt;00:00, 3.88MB/s]"}}, "a12e6b16c889417886e6384fad789932": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84d7975247f746c48402f3dcbcc9985c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6fdcadb7d7f400aa63fc1e613a3dcc4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "eb7c4700e9114cfabc36a3f10e580b43": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2b49618b1ae3460291fccf51d00688ee": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f363fe5110b74ad89c3d4b670ab57b9f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bcced56d7dbe438c8b1d04037f6b7558": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f571c42002df425281d6f93029df92cd": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7fc8b575514d42bda2704ca37c693059", "IPY_MODEL_e51a514093304c20b9ef4ba8576902e8", "IPY_MODEL_fda4d47309674b97b948f13f242a02ff"], "layout": "IPY_MODEL_ab15036842cb4181a21acc5220d879e1"}}, "7fc8b575514d42bda2704ca37c693059": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e749bdef3b214d1487ccf2ca4f55e7ad", "placeholder": "​", "style": "IPY_MODEL_9de7ac5f1566419caa76ef80fefbe864", "value": "tokenizer.json: 100%"}}, "e51a514093304c20b9ef4ba8576902e8": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7dd7c43da93f43f4abaf620fcb77fdff", "max": 466247, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_68dfb6915eab4e32b43032e795d30c88", "value": 466247}}, "fda4d47309674b97b948f13f242a02ff": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_84d3ec9956924edf98ee61a2cd722903", "placeholder": "​", "style": "IPY_MODEL_b03b63087ff94d1f826fa10efad2e287", "value": " 466k/466k [00:00&lt;00:00, 17.1MB/s]"}}, "ab15036842cb4181a21acc5220d879e1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e749bdef3b214d1487ccf2ca4f55e7ad": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9de7ac5f1566419caa76ef80fefbe864": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7dd7c43da93f43f4abaf620fcb77fdff": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68dfb6915eab4e32b43032e795d30c88": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "84d3ec9956924edf98ee61a2cd722903": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b03b63087ff94d1f826fa10efad2e287": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "478779d52d1342c98d4b58f7793525ca": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_af0356140c8f4ea9aa0854302f1a5a16", "IPY_MODEL_c0889a40ae5a4f46bd22b5e2be36893d", "IPY_MODEL_71a876e5968d439691ca885c743cd9ee"], "layout": "IPY_MODEL_d0d27eb46dd9434d962cd59f63c90ad5"}}, "af0356140c8f4ea9aa0854302f1a5a16": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2d68f023cff04b8892eb7320052b7cf8", "placeholder": "​", "style": "IPY_MODEL_84685055dee943f795f948f0f014be60", "value": "special_tokens_map.json: 100%"}}, "c0889a40ae5a4f46bd22b5e2be36893d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_898bf12506ff4f4185499baafc20c474", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_39562b44ddad4afeafeccba4c8f3a758", "value": 112}}, "71a876e5968d439691ca885c743cd9ee": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_76077964fc254b0aad053653d915a440", "placeholder": "​", "style": "IPY_MODEL_64b9ab6dbc5448b8ba6999626edc9806", "value": " 112/112 [00:00&lt;00:00, 9.55kB/s]"}}, "d0d27eb46dd9434d962cd59f63c90ad5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2d68f023cff04b8892eb7320052b7cf8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84685055dee943f795f948f0f014be60": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "898bf12506ff4f4185499baafc20c474": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "39562b44ddad4afeafeccba4c8f3a758": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "76077964fc254b0aad053653d915a440": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64b9ab6dbc5448b8ba6999626edc9806": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2ef5efa3119c4036989f967dc83e46ce": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_17a45c813b044d22821e7601584a7c03", "IPY_MODEL_04c6d1b69aa344ea8c65383128e87c92", "IPY_MODEL_e331664fedc24fa5b02886700c8eef99"], "layout": "IPY_MODEL_8da3c08d287943e7b22ff1cceb66b25a"}}, "17a45c813b044d22821e7601584a7c03": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4d35fd063fa94ff8a6943308b52eb921", "placeholder": "​", "style": "IPY_MODEL_f044b6943e234bedb079cd4b8be543ef", "value": "config.json: 100%"}}, "04c6d1b69aa344ea8c65383128e87c92": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_095756276f494a52851456e2f173021a", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0b0d33c42a0d4bd9a35d9f0e02b333f2", "value": 190}}, "e331664fedc24fa5b02886700c8eef99": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_14a28c11165c472488cc340ce1142189", "placeholder": "​", "style": "IPY_MODEL_e3e1032b5d244aa0a369017042834c26", "value": " 190/190 [00:00&lt;00:00, 13.8kB/s]"}}, "8da3c08d287943e7b22ff1cceb66b25a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d35fd063fa94ff8a6943308b52eb921": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f044b6943e234bedb079cd4b8be543ef": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "095756276f494a52851456e2f173021a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0b0d33c42a0d4bd9a35d9f0e02b333f2": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "14a28c11165c472488cc340ce1142189": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e3e1032b5d244aa0a369017042834c26": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a8f4043b1d194d519697c8f460576343": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a3d7bc4c33fb4a0c97b88c5fad55f006", "IPY_MODEL_85bf358df1684c5785707501e1f65b2b", "IPY_MODEL_9984fe8bdb8a4c3b8190a53fa97722d1"], "layout": "IPY_MODEL_417951d37a4b47c593320c313104e08d"}}, "a3d7bc4c33fb4a0c97b88c5fad55f006": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb6dcc559b57491e97391f22a06c9a10", "placeholder": "​", "style": "IPY_MODEL_dac947f16e2745d4a8a9d9a75a4e93a2", "value": "Batches: 100%"}}, "85bf358df1684c5785707501e1f65b2b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_40aac98762f14246ac1a03ef27786f33", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e02f702101ce45c08ced253aacb3277a", "value": 1}}, "9984fe8bdb8a4c3b8190a53fa97722d1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_026f7ee73bfc4b91a6d2234d5cf20aac", "placeholder": "​", "style": "IPY_MODEL_043f406fce684fc3a0ee4411827edde4", "value": " 1/1 [00:00&lt;00:00,  3.81it/s]"}}, "417951d37a4b47c593320c313104e08d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb6dcc559b57491e97391f22a06c9a10": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dac947f16e2745d4a8a9d9a75a4e93a2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "40aac98762f14246ac1a03ef27786f33": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e02f702101ce45c08ced253aacb3277a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "026f7ee73bfc4b91a6d2234d5cf20aac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "043f406fce684fc3a0ee4411827edde4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}