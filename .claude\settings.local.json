{"permissions": {"allow": ["Bash(cd:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(python:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(rm:*)", "<PERSON><PERSON>(true)", "Bash(./package-extension.sh:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rg:*)", "Bash(/Users/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"Script Builder\" docs/md_v2/apps/crawl4ai-assistant/)", "Bash(/Users/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 30 \"generateCode\\(events, format\\)\" docs/md_v2/apps/crawl4ai-assistant/content/content.js)", "Bash(/Users/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"<style>\" docs/md_v2/apps/crawl4ai-assistant/index.html -A 5)", "Bash(git checkout:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(./test-final-integration.sh:*)", "<PERSON><PERSON>(mv:*)"]}, "enableAllProjectMcpServers": false}